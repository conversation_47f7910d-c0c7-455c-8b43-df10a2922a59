import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { generateOutline, generateChapterContent } from "./gemini";
import { OutlineGenerationParams, ContentGenerationParams } from "@shared/types";

export async function registerRoutes(app: Express): Promise<Server> {
  // API routes for eBook generation
  app.post("/api/generate-outline", async (req, res) => {
    try {
      const { userInput, generationParams } = req.body;
      
      if (!userInput || typeof userInput !== "string") {
        return res.status(400).json({ 
          message: "Invalid input. Please provide a valid 'userInput' string." 
        });
      }

      // Validate generation parameters
      const params: OutlineGenerationParams = generationParams || {
        maxChapters: 10,
        maxSubChapters: 5
      };

      const outline = await generateOutline(userInput, params);
      return res.json({ outline });
    } catch (error) {
      console.error("Error generating outline:", error);
      return res.status(500).json({ 
        message: "Failed to generate outline. Please try again." 
      });
    }
  });

  app.post("/api/generate-chapter", async (req, res) => {
    try {
      const { 
        subChapterTitle, 
        mainChapterTitle, 
        bookTopic, 
        generationParams 
      } = req.body;
      
      if (!subChapterTitle || !mainChapterTitle || !bookTopic) {
        return res.status(400).json({ 
          message: "Missing required fields. Please provide subChapterTitle, mainChapterTitle, and bookTopic." 
        });
      }

      // Default parameters if not provided
      const params: ContentGenerationParams = generationParams || {
        tone: 'professional',
        style: 'descriptive',
        language: 'intermediate'
      };

      const subChapterContent = await generateChapterContent(
        subChapterTitle,
        mainChapterTitle,
        bookTopic,
        params
      );
      
      return res.json({ subChapterContent });
    } catch (error) {
      console.error("Error generating chapter content:", error);
      return res.status(500).json({ 
        message: "Failed to generate chapter content. Please try again." 
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
