export interface Chapter {
  title: string;
  subchapters: string[];
}

export interface BookOutline {
  chapters: Chapter[];
}

export interface GeneratedContent {
  mainChapterTitle: string;
  subChapterTitle: string;
  content: string;
  index: number;
}

export type WritingTone = 'professional' | 'casual' | 'academic' | 'conversational' | 'instructional';
export type WritingStyle = 'descriptive' | 'analytical' | 'persuasive' | 'narrative' | 'technical';
export type WritingLanguage = 'simple' | 'intermediate' | 'advanced' | 'technical';

export interface OutlineGenerationParams {
  maxChapters: number;
  maxSubChapters: number;
  tone?: WritingTone;
  style?: WritingStyle;
  language?: WritingLanguage;
}

export interface ContentGenerationParams {
  tone: WritingTone;
  style: WritingStyle;
  language: WritingLanguage;
}
