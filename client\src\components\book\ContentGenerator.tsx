import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { apiRequest } from "@/lib/queryClient";
import { GeneratedContent, ContentGenerationParams, WritingTone, WritingStyle, WritingLanguage, BookOutline } from "@shared/types";
import { CheckIcon, ChevronRightIcon, EditIcon, RefreshCcwIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ContentGeneratorProps {
  selectedChapter: {
    mainChapterTitle: string;
    subChapterTitle: string;
    index: number;
    chapterIndex: number;
    subChapterIndex: number;
  } | null;
  bookTopic: string;
  generatedContent: GeneratedContent | null;
  onContentGenerated: (content: string) => void;
  contentTone: WritingTone;
  contentStyle: WritingStyle;
  contentLanguage: WritingLanguage;
  outline?: BookOutline | null;
  generatedChapters?: Record<string, boolean>;
  onChapterSelect?: (mainChapterTitle: string, subChapterTitle: string, index: number, chapterIndex: number, subChapterIndex: number) => void;
  nextChapterToGenerate?: { chapterIndex: number; subChapterIndex: number };
}

export default function ContentGenerator({
  selectedChapter,
  bookTopic,
  generatedContent,
  onContentGenerated,
  contentTone,
  contentStyle,
  contentLanguage,
  outline,
  generatedChapters,
  onChapterSelect,
  nextChapterToGenerate
}: ContentGeneratorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    if (selectedChapter) {
      // Check if content already exists in localStorage
      const storageKey = `${selectedChapter.mainChapterTitle}-${selectedChapter.subChapterTitle}`;
      const existingContent = localStorage.getItem(`chapter-content-${storageKey}`);
      
      if (existingContent) {
        // If content exists in localStorage, use it instead of generating new content
        onContentGenerated(existingContent);
      } else if (!generatedContent) {
        // Only generate new content if it doesn't exist in localStorage
        generateContent();
      }
    }
  }, [selectedChapter]);

  const generateContent = async () => {
    if (!selectedChapter || !bookTopic) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const res = await apiRequest('POST', '/api/generate-chapter', {
        subChapterTitle: selectedChapter.subChapterTitle,
        mainChapterTitle: selectedChapter.mainChapterTitle,
        bookTopic,
        generationParams: {
          tone: contentTone,
          style: contentStyle,
          language: contentLanguage
        }
      });
      
      const data = await res.json();
      onContentGenerated(data.subChapterContent);
      
      toast({
        title: "Content generated",
        description: "Your chapter content has been created successfully",
      });
    } catch (err) {
      console.error("Error generating content:", err);
      setError("Failed to generate content. Please try again.");
      
      toast({
        title: "Generation failed",
        description: "Could not generate chapter content. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const regenerateContent = async () => {
    if (!selectedChapter) return;
    setIsLoading(true);
    setError(null);

    try {
      // Clear any existing content from localStorage before regenerating
      if (selectedChapter) {
        const storageKey = `${selectedChapter.mainChapterTitle}-${selectedChapter.subChapterTitle}`;
        localStorage.removeItem(`chapter-content-${storageKey}`);
      }
      
      await generateContent();
    } catch (err) {
      console.error("Error regenerating content:", err);
      setError("Failed to regenerate content. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper to get user-friendly descriptions of the content parameters
  const getContentStyleDescription = () => {
    const toneLabels: Record<WritingTone, string> = {
      professional: "Professional",
      casual: "Casual",
      academic: "Academic",
      conversational: "Conversational",
      instructional: "Instructional"
    };
    
    const styleLabels: Record<WritingStyle, string> = {
      descriptive: "Descriptive",
      analytical: "Analytical",
      persuasive: "Persuasive",
      narrative: "Narrative",
      technical: "Technical"
    };
    
    const languageLabels: Record<WritingLanguage, string> = {
      simple: "Simple",
      intermediate: "Intermediate",
      advanced: "Advanced",
      technical: "Technical"
    };
    
    return {
      tone: toneLabels[contentTone],
      style: styleLabels[contentStyle],
      language: languageLabels[contentLanguage]
    };
  };

  // Rendering helper for markdown content
  const renderMarkdown = (content: string) => {
    if (!content) return "";
    
    // Basic markdown processing for headers, paragraphs, lists
    const processedContent = content
      .replace(/^### (.*$)/gim, '<h3 class="font-semibold text-xl mb-3 mt-6">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="font-semibold text-2xl mb-4">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="font-bold text-3xl mb-5">$1</h1>')
      .replace(/^\> (.*)$/gim, '<blockquote class="border-l-4 border-primary/30 pl-4 italic my-4">$1</blockquote>')
      .replace(/^\- (.*)$/gim, '<li class="ml-6 list-disc my-1">$1</li>')
      .replace(/^\d\. (.*)$/gim, '<li class="ml-6 list-decimal my-1">$1</li>')
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      .replace(/\n\n/gim, '</p><p class="mb-4">');
    
    return `<p class="mb-4">${processedContent}</p>`;
  };

  const styleInfo = getContentStyleDescription();

  return (
    <div className="p-6 flex-1 flex flex-col">
      {/* Content Header */}
      {selectedChapter && (
        <div className="flex justify-between items-start mb-4">
          <div>
            <div className="text-sm text-muted-foreground font-medium mb-1">
              Chapter {selectedChapter.index}
            </div>
            <h2 className="text-xl md:text-2xl font-semibold">
              {selectedChapter.subChapterTitle}
            </h2>
            
            {/* Display content style when content is generated */}
            {generatedContent && !isLoading && (
              <div className="flex flex-wrap gap-2 mt-2">
                <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                  {styleInfo.tone}
                </div>
                <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                  {styleInfo.style}
                </div>
                <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                  {styleInfo.language}
                </div>
              </div>
            )}
          </div>
          
          {/* Generation Status */}
          {generatedContent && !isLoading && (
            <div className="bg-green-500/20 px-3 py-1 rounded-full text-green-500 text-xs flex items-center space-x-1">
              <CheckIcon className="h-4 w-4" />
              <span>Generated</span>
            </div>
          )}
        </div>
      )}
      
      {/* Loading State for Content */}
      {isLoading && (
        <div className="flex-1 flex justify-center items-center">
          <div className="text-center">
            <div className="animate-spin h-10 w-10 border-4 border-primary/30 border-t-primary rounded-full mx-auto"></div>
            <p className="mt-4 text-muted-foreground">Generating chapter content...</p>
            <p className="text-xs text-muted-foreground mt-2">
              Style: {styleInfo.tone}, {styleInfo.style}, {styleInfo.language}
            </p>
          </div>
        </div>
      )}
      
      {/* Error State for Content */}
      {error && !isLoading && (
        <div className="flex-1 flex justify-center items-center">
          <div className="text-center bg-destructive/10 p-6 rounded-lg max-w-md">
            <svg className="h-12 w-12 text-destructive mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-2 text-lg font-medium text-destructive">Generation Failed</h3>
            <p className="mt-1 text-muted-foreground">
              We couldn't generate content for this chapter. Please try again or select a different chapter.
            </p>
            <Button variant="outline" className="mt-4" onClick={regenerateContent}>
              Try Again
            </Button>
          </div>
        </div>
      )}
      
      {/* Empty State for Content */}
      {!selectedChapter && !isLoading && !error && (
        <div className="flex-1 flex justify-center items-center">
          <div className="text-center max-w-md">
            <svg className="h-16 w-16 text-muted-foreground mx-auto opacity-40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="mt-2 text-lg font-medium">No Content Selected</h3>
            <p className="mt-1 text-muted-foreground">Select a sub-chapter from the outline to generate content</p>
          </div>
        </div>
      )}
      
      {/* Generated Content */}
      {generatedContent && !isLoading && !error && (
        <div className="flex-1 bg-card p-6 rounded-lg border border-muted overflow-y-auto">
          <article 
            className="prose prose-invert max-w-none" 
            dangerouslySetInnerHTML={{ 
              __html: renderMarkdown(generatedContent.content || "")
            }}
          />
        </div>
      )}
      
      {/* Content Actions */}
      {generatedContent && !isLoading && !error && (
        <div className="mt-4 flex justify-between">
          <div className="flex space-x-2">
            <Button variant="outline" className="flex items-center gap-1">
              <EditIcon className="h-4 w-4" />
              <span>Edit</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="flex items-center gap-1"
              onClick={regenerateContent}
            >
              <RefreshCcwIcon className="h-4 w-4" />
              <span>Regenerate</span>
            </Button>
          </div>
          
          <div className="flex space-x-2">
            <Button variant="outline" className="flex items-center gap-1">
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
              </svg>
              <span>Export</span>
            </Button>
            
            <Button 
              className="flex items-center gap-1"
              onClick={() => {
                if (!selectedChapter || !outline) return;
                
                // Find the next chapter in sequence
                let nextChapterIndex = selectedChapter.chapterIndex;
                let nextSubChapterIndex = selectedChapter.subChapterIndex + 1;
                
                // If we've reached the end of subchapters in this chapter, move to the next chapter
                if (nextSubChapterIndex >= outline.chapters[nextChapterIndex].subchapters.length) {
                  nextChapterIndex++;
                  nextSubChapterIndex = 0;
                }
                
                // Only proceed if we haven't reached the end of all chapters
                if (nextChapterIndex < outline.chapters.length) {
                  const nextChapter = outline.chapters[nextChapterIndex];
                  const nextSubChapter = nextChapter.subchapters[nextSubChapterIndex];
                  
                  // Check if this next chapter is already generated
                  const isGenerated = generatedChapters ? generatedChapters[`${nextChapter.title}-${nextSubChapter}`] : false;
                  
                  if (isGenerated || (nextChapterIndex === nextChapterToGenerate?.chapterIndex && 
                      nextSubChapterIndex === nextChapterToGenerate?.subChapterIndex)) {
                    onChapterSelect && onChapterSelect(
                      nextChapter.title,
                      nextSubChapter,
                      nextChapterIndex + 1,
                      nextChapterIndex,
                      nextSubChapterIndex
                    );
                  } else {
                    toast({
                      title: "Sequential generation required",
                      description: "Please generate content for chapters in sequential order.",
                      variant: "destructive"
                    });
                  }
                } else {
                  toast({
                    title: "End of book reached",
                    description: "You've reached the end of your book outline.",
                  });
                }
              }}
            >
              <ChevronRightIcon className="h-4 w-4" />
              <span>Next Chapter</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
