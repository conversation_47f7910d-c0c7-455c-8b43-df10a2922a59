import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { BookOutline, Chapter, OutlineGenerationParams, WritingTone, WritingStyle, WritingLanguage } from "@shared/types";
import { apiRequest } from "@/lib/queryClient";
import { Textarea } from "@/components/ui/textarea";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { CheckIcon, EyeIcon, Settings2Icon, ArrowRightIcon, Loader2Icon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import GenerationOptions from "./GenerationOptions";

interface OutlineGeneratorProps {
  onOutlineGenerated: (outline: BookOutline) => void;
  onBookTopicChange: (topic: string) => void;
  bookTopic: string;
  outline: BookOutline | null;
  onChapterSelect: (mainChapterTitle: string, subChapterTitle: string, index: number, chapterIndex: number, subChapterIndex: number) => void;
  generatedChapters: Record<string, boolean>;
  generatedCount: number;
  totalCount: number;
  outlineParams: OutlineGenerationParams;
  onOutlineParamsChange: (params: OutlineGenerationParams) => void;
  contentTone: WritingTone;
  onContentToneChange: (tone: WritingTone) => void;
  contentStyle: WritingStyle;
  onContentStyleChange: (style: WritingStyle) => void;
  contentLanguage: WritingLanguage;
  onContentLanguageChange: (language: WritingLanguage) => void;
  nextChapterToGenerate?: { chapterIndex: number; subChapterIndex: number };
}

export default function OutlineGenerator({
  onOutlineGenerated,
  onBookTopicChange,
  bookTopic,
  outline,
  onChapterSelect,
  generatedChapters,
  generatedCount,
  totalCount,
  outlineParams,
  onOutlineParamsChange,
  contentTone,
  onContentToneChange,
  contentStyle,
  onContentStyleChange,
  contentLanguage,
  onContentLanguageChange,
  nextChapterToGenerate
}: OutlineGeneratorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showOptions, setShowOptions] = useState(false);
  const [generatingAllSubChapters, setGeneratingAllSubChapters] = useState<{chapterIndex: number, inProgress: boolean, error: boolean}>({chapterIndex: -1, inProgress: false, error: false});
  const [currentSubChapterIndex, setCurrentSubChapterIndex] = useState<number>(0);
  const { toast } = useToast();

  const generateOutline = async () => {
    if (!bookTopic?.trim()) {
      toast({
        title: "Input required",
        description: "Please enter a book topic or keywords",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const res = await apiRequest('POST', '/api/generate-outline', { 
        userInput: bookTopic,
        generationParams: outlineParams
      });
      const data = await res.json();
      onOutlineGenerated(data.outline);
      toast({
        title: "Outline generated",
        description: "Your book outline has been created successfully",
      });
    } catch (err) {
      console.error("Error generating outline:", err);
      setError("Failed to generate outline. Please try again.");
      toast({
        title: "Generation failed",
        description: "Could not generate outline. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // State to track completed chapters (for showing green success state)
  const [completedChapters, setCompletedChapters] = useState<number[]>([]);
  
  // Function to generate all sub-chapters for a main chapter sequentially
  const generateAllSubChapters = async (chapterIndex: number) => {
    if (!outline) return;
    
    const chapter = outline.chapters[chapterIndex];
    if (!chapter) return;
    
    // Reset any previous error state
    setGeneratingAllSubChapters({chapterIndex, inProgress: true, error: false});
    setCurrentSubChapterIndex(0);
    
    try {
      // Find the first sub-chapter that hasn't been generated yet
      let startIndex = 0;
      while (startIndex < chapter.subchapters.length && generatedChapters[`${chapter.title}-${chapter.subchapters[startIndex]}`]) {
        startIndex++;
      }
      
      // If all sub-chapters are already generated, just show a message
      if (startIndex >= chapter.subchapters.length) {
        toast({
          title: "Already generated",
          description: "All sub-chapters in this chapter have already been generated."
        });
        setGeneratingAllSubChapters({chapterIndex: -1, inProgress: false, error: false});
        // Add to completed chapters if not already there
        if (!completedChapters.includes(chapterIndex)) {
          setCompletedChapters([...completedChapters, chapterIndex]);
        }
        return;
      }
      
      setCurrentSubChapterIndex(startIndex);
      
      // Generate the first sub-chapter to start the sequence
      onChapterSelect(
        chapter.title,
        chapter.subchapters[startIndex],
        chapterIndex + 1,
        chapterIndex,
        startIndex
      );
    } catch (err) {
      console.error("Error generating sub-chapters:", err);
      toast({
        title: "Generation failed",
        description: "Failed to generate sub-chapters. Please try again.",
        variant: "destructive"
      });
      setGeneratingAllSubChapters({chapterIndex, inProgress: false, error: true});
    }
  };
  
  // Helper function to check if all sub-chapters in a chapter are generated
  const isAllSubChaptersGenerated = (chapter: Chapter) => {
    return chapter.subchapters.every(subchapter => 
      generatedChapters[`${chapter.title}-${subchapter}`]
    );
  };

  // Helper function to check if all sub-chapters in the previous chapter are generated
  const isAllPreviousChapterGenerated = (currentChapterIndex: number) => {
    if (!outline || currentChapterIndex <= 0) return true;
    
    const previousChapter = outline.chapters[currentChapterIndex - 1];
    return isAllSubChaptersGenerated(previousChapter);
  };

  // Effect to monitor changes in generatedChapters and continue sequential generation
  useEffect(() => {
    // Only proceed if we're in the middle of generating all sub-chapters
    if (!generatingAllSubChapters.inProgress || !outline || generatingAllSubChapters.error) return;
    
    const chapterIndex = generatingAllSubChapters.chapterIndex;
    const chapter = outline.chapters[chapterIndex];
    if (!chapter) return;
    
    // Check if the current sub-chapter has been generated
    const currentSubChapter = chapter.subchapters[currentSubChapterIndex];
    const isCurrentGenerated = generatedChapters[`${chapter.title}-${currentSubChapter}`];
    
    // If current sub-chapter is generated, move to the next one
    if (isCurrentGenerated) {
      const nextSubIndex = currentSubChapterIndex + 1;
      
      // If we've generated all sub-chapters in this chapter, we're done
      if (nextSubIndex >= chapter.subchapters.length) {
        toast({
          title: "Generation complete",
          description: `All sub-chapters in "${chapter.title}" have been generated.`
        });
        setGeneratingAllSubChapters({chapterIndex: -1, inProgress: false, error: false});
        
        // Add to completed chapters for the green success state
        if (!completedChapters.includes(chapterIndex)) {
          setCompletedChapters([...completedChapters, chapterIndex]);
        }
        return;
      }
      
      // Otherwise, generate the next sub-chapter
      try {
        setCurrentSubChapterIndex(nextSubIndex);
        onChapterSelect(
          chapter.title,
          chapter.subchapters[nextSubIndex],
          chapterIndex + 1,
          chapterIndex,
          nextSubIndex
        );
      } catch (err) {
        console.error("Error generating next sub-chapter:", err);
        toast({
          title: "Generation failed",
          description: "Failed to generate the next sub-chapter. Please try again.",
          variant: "destructive"
        });
        setGeneratingAllSubChapters({chapterIndex, inProgress: false, error: true});
      }
    }
  }, [generatedChapters, generatingAllSubChapters, currentSubChapterIndex, outline, onChapterSelect, completedChapters]);

  return (
    <div className="p-4">
      {/* Topic Input */}
      <div className="mb-4">
        <label htmlFor="book-topic" className="block text-sm font-medium text-muted-foreground mb-1">
          Book Topic or Keywords
        </label>
        <Textarea
          id="book-topic"
          className="w-full resize-none"
          rows={3}
          placeholder="Enter your book topic or keywords here..."
          value={bookTopic}
          onChange={(e) => onBookTopicChange(e.target.value)}
        />
        
        <div className="mt-2 flex flex-col space-y-2">
          <Button 
            className="w-full" 
            onClick={generateOutline}
            disabled={isLoading}
          >
            {isLoading ? "Generating..." : "Generate Outline"}
          </Button>
          
          <Button 
            variant="outline" 
            type="button" 
            className="w-full flex items-center justify-center gap-1"
            onClick={() => setShowOptions(!showOptions)}
          >
            <Settings2Icon className="h-4 w-4" />
            <span>Customization Options</span>
            <ArrowRightIcon className={`h-4 w-4 ml-1 transition-transform ${showOptions ? 'rotate-90' : ''}`} />
          </Button>
        </div>
      </div>
      
      {/* Generation Options */}
      {showOptions && (
        <div className="mb-6 animate-in fade-in duration-300">
          <GenerationOptions
            outlineParams={outlineParams}
            onOutlineParamsChange={onOutlineParamsChange}
            contentTone={contentTone}
            onContentToneChange={onContentToneChange}
            contentStyle={contentStyle}
            onContentStyleChange={onContentStyleChange}
            contentLanguage={contentLanguage}
            onContentLanguageChange={onContentLanguageChange}
          />
        </div>
      )}
      
      {/* Outline Container */}
      <div className="mt-6 border border-muted rounded-md overflow-hidden">
        <div className="p-3 bg-muted/30 flex justify-between items-center">
          <h3 className="font-medium">Book Outline</h3>
          {outline && (
            <div className="text-xs text-muted-foreground px-2 py-1 bg-background rounded-md">
              <span>{generatedCount}/{totalCount}</span> chapters generated
            </div>
          )}
        </div>
        
        {/* Loading State for Outline */}
        {isLoading && (
          <div className="py-12 flex justify-center">
            <div className="animate-spin h-8 w-8 border-4 border-primary/30 border-t-primary rounded-full"></div>
          </div>
        )}
        
        {/* Error State for Outline */}
        {error && !isLoading && (
          <div className="p-6 text-center">
            <div className="bg-destructive/20 p-4 rounded-md inline-block">
              <svg className="h-6 w-6 text-destructive mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="mt-2 text-destructive text-sm">{error}</p>
            </div>
          </div>
        )}
        
        {/* Empty State for Outline */}
        {!outline && !isLoading && !error && (
          <div className="py-10 text-center">
            <svg className="h-12 w-12 text-muted-foreground mx-auto mb-3 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-muted-foreground">Enter your topic and generate a book outline</p>
          </div>
        )}
        
        {/* Accordion for Outline */}
        {outline && !isLoading && (
          <Accordion type="multiple" className="w-full">
            {outline.chapters.map((chapter, chapterIndex) => (
              <AccordionItem key={`chapter-${chapterIndex}`} value={`chapter-${chapterIndex}`}>
                <AccordionTrigger className="px-4 py-3 bg-card/50 hover:bg-muted/20">
                  <span className="font-medium text-left">
                    {chapterIndex + 1}. {chapter.title}
                  </span>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="p-4 pt-2 bg-card border-t border-muted">
                    <Button 
                       variant={
                         generatingAllSubChapters.error && generatingAllSubChapters.chapterIndex === chapterIndex
                           ? "error"
                           : generatingAllSubChapters.inProgress && generatingAllSubChapters.chapterIndex === chapterIndex
                             ? "generating"
                             : completedChapters.includes(chapterIndex) || isAllSubChaptersGenerated(chapter)
                               ? "generated"
                               : "default"
                       }
                       size="sm" 
                       className={`mb-2 text-xs w-full transition-all duration-300 ${completedChapters.includes(chapterIndex) || isAllSubChaptersGenerated(chapter) ? 'opacity-90' : ''}`}
                       onClick={() => {
                         // If in error state, retry generation
                         if (generatingAllSubChapters.error && generatingAllSubChapters.chapterIndex === chapterIndex) {
                           generateAllSubChapters(chapterIndex);
                         } else {
                           generateAllSubChapters(chapterIndex);
                         }
                       }}
                       disabled={
                         // Disable during generation but not in error state
                         (generatingAllSubChapters.inProgress && !generatingAllSubChapters.error) || 
                         // Disable if not all sub-chapters in previous chapter are generated
                         (chapterIndex > 0 && !isAllPreviousChapterGenerated(chapterIndex)) ||
                         // Disable if all sub-chapters in this chapter are already generated
                         isAllSubChaptersGenerated(chapter) ||
                         // Disable if this chapter is in completed state
                         completedChapters.includes(chapterIndex)
                       }
                     >
                       {generatingAllSubChapters.error && generatingAllSubChapters.chapterIndex === chapterIndex ? (
                         <>Generation Failed - Retry</>
                       ) : generatingAllSubChapters.inProgress && generatingAllSubChapters.chapterIndex === chapterIndex ? (
                         <>
                           <Loader2Icon className="h-3 w-3 mr-1 animate-spin" />
                           Generating...
                         </>
                       ) : completedChapters.includes(chapterIndex) || isAllSubChaptersGenerated(chapter) ? (
                         <>
                           <CheckIcon className="h-3 w-3 mr-1" />
                           Generated All Sub-Chapters
                         </>
                       ) : (
                         <>Generate All Sub-Chapters</>
                       )}
                     </Button>
                    <ul className="space-y-1">
                      {chapter.subchapters.map((subchapter, subIndex) => {
                        const isGenerated = generatedChapters[`${chapter.title}-${subchapter}`];
                        
                        // Determine if this subchapter is the next one to generate
                        const isNextInSequence = nextChapterToGenerate && 
                          chapterIndex === nextChapterToGenerate.chapterIndex && 
                          subIndex === nextChapterToGenerate.subChapterIndex;
                        
                        // First chapter is always enabled if no chapters have been generated
                        const isFirstChapter = chapterIndex === 0 && subIndex === 0 && Object.keys(generatedChapters).length === 0;
                        
                        // Determine if this subchapter should be enabled
                        const isEnabled = isGenerated || isNextInSequence || isFirstChapter;
                        
                        return (
                          <li 
                            key={`subchapter-${chapterIndex}-${subIndex}`}
                            className={`flex justify-between items-center p-2 rounded group ${isEnabled ? 'cursor-pointer hover:bg-background' : 'opacity-50 cursor-not-allowed'}`}
                            onClick={() => {
                              if (isGenerated) {
                                // If already generated, just display the content
                                onChapterSelect(chapter.title, subchapter, chapterIndex + 1, chapterIndex, subIndex);
                              } else if (isNextInSequence || isFirstChapter) {
                                // If it's the next in sequence, allow generation
                                onChapterSelect(chapter.title, subchapter, chapterIndex + 1, chapterIndex, subIndex);
                              } else {
                                // Show toast for sequential generation requirement
                                toast({
                                  title: "Sequential generation required",
                                  description: "Please generate content for chapters in sequential order.",
                                  variant: "destructive"
                                });
                              }
                            }}
                          >
                            <span className="text-sm">
                              {chapterIndex + 1}.{subIndex + 1} {subchapter}
                            </span>
                            <div className="flex space-x-1">
                              {isGenerated ? (
                                <span className="text-green-500">
                                  <CheckIcon className="h-5 w-5" />
                                </span>
                              ) : (
                                <>
                                  {isNextInSequence || isFirstChapter ? (
                                    <div className="flex items-center">
                                      <span className="text-primary/70 group-hover:inline-block">
                                        <EyeIcon className="h-5 w-5" />
                                      </span>
                                      <span className="ml-1 text-xs text-blue-400 font-medium">Next</span>
                                    </div>
                                  ) : (
                                    <span className="text-muted-foreground">
                                      <EyeIcon className="h-5 w-5 opacity-30" />
                                    </span>
                                  )}
                                </>
                              )}
                            </div>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
      </div>
    </div>
  );
}
