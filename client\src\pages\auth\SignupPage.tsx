import { useEffect, useMemo, useState } from "react";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

function isValidEmail(email: string) {
  // basic RFC5322-lite regex
  return /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/.test(email);
}

function passwordChecks(pw: string) {
  return {
    length: pw.length >= 8,
    letters: /[A-Za-z]/.test(pw),
    numbers: /[0-9]/.test(pw),
  };
}

export default function SignupPage() {
  const { signup, loginWithGoogle, error, redirectToApp } = useAuth();
  const [email, setEmail] = useState("");
  const [disposable, setDisposable] = useState<null | boolean>(null);
  const [emailChecking, setEmailChecking] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [_, setLocation] = useLocation();
  const [localMessage, setLocalMessage] = useState<string | null>(null);

  const checks = useMemo(() => passwordChecks(password), [password]);
  const canSubmit =
    !emailChecking && !disposable && isValidEmail(email) && checks.length && checks.letters && checks.numbers && password === confirmPassword;

  useEffect(() => {
    if (!email || !isValidEmail(email)) {
      setDisposable(null);
      return;
    }
    const ctrl = new AbortController();
    setEmailChecking(true);
    const t = setTimeout(async () => {
      try {
        const res = await fetch(`https://disposable.debounce.io/?email=${encodeURIComponent(email)}`, { signal: ctrl.signal });
        const data = (await res.json()) as { disposable?: "true" | "false" };
        setDisposable(data.disposable === "true");
      } catch {
        // ignore network errors; do not block signup
        setDisposable(null);
      } finally {
        setEmailChecking(false);
      }
    }, 500);
    return () => {
      ctrl.abort();
      clearTimeout(t);
    };
  }, [email]);

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLocalMessage(null);
    if (!isValidEmail(email)) {
      setLocalMessage("Please enter a valid email address.");
      return;
    }
    if (disposable) {
      setLocalMessage("Disposable email addresses are not allowed.");
      return;
    }
    if (password !== confirmPassword) {
      setLocalMessage("Passwords do not match");
      return;
    }
    if (!(checks.length && checks.letters && checks.numbers)) {
      setLocalMessage("Password must be at least 8 chars and include letters and numbers.");
      return;
    }
    setLoading(true);
    try {
      await signup(email, password);
      setLocalMessage("Verification email sent. Please check your inbox.");
      setTimeout(() => setLocation("/login"), 1500);
    } finally {
      setLoading(false);
    }
  };

  const onGoogle = async () => {
    setLoading(true);
    try {
      await loginWithGoogle();
      // Redirect will be handled by ProtectedRoute based on email verification status
      redirectToApp();
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center py-16 px-4">
      <div className="glass-card w-full max-w-md rounded-xl p-8">
        <h2 className="text-2xl font-semibold mb-1">Create your account</h2>
        <p className="text-sm text-muted-foreground mb-6">Join AIeBookWriter.Pro</p>
        {(error || localMessage) && (
          <div className={`mb-4 text-sm ${error ? 'text-red-400' : 'text-green-400'}`}>
            {error || localMessage}
          </div>
        )}
        <form onSubmit={onSubmit} className="space-y-4">
          <div>
            <label className="block text-sm mb-1">Email</label>
            <Input
              className="glass-input"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              placeholder="<EMAIL>"
            />
            {email && !isValidEmail(email) && (
              <p className="text-xs text-red-400 mt-1">Please enter a valid email.</p>
            )}
            {email && emailChecking && (
              <p className="text-xs text-muted-foreground mt-1">Checking email…</p>
            )}
            {disposable === true && (
              <p className="text-xs text-red-400 mt-1">Disposable emails are not allowed.</p>
            )}
          </div>
          <div>
            <label className="block text-sm mb-1">Password</label>
            <Input
              className="glass-input"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              placeholder="At least 8 characters"
              minLength={8}
            />
            <div className="text-xs mt-1 space-y-0.5">
              <p className={checks.length ? "text-green-400" : "text-red-400"}>• At least 8 characters</p>
              <p className={checks.letters ? "text-green-400" : "text-red-400"}>• Contains letters</p>
              <p className={checks.numbers ? "text-green-400" : "text-red-400"}>• Contains numbers</p>
            </div>
          </div>
          <div>
            <label className="block text-sm mb-1">Confirm Password</label>
            <Input
              className="glass-input"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
            {confirmPassword && password !== confirmPassword && (
              <p className="text-xs text-red-400 mt-1">Passwords do not match.</p>
            )}
          </div>
          <Button className="w-full gradient-button" type="submit" disabled={loading || !canSubmit}>
            {loading ? "Creating…" : "Sign Up"}
          </Button>
        </form>
        <div className="mt-4">
          <Button variant="outline" className="w-full flex items-center justify-center gap-2" onClick={onGoogle} disabled={loading}>
            <span className="bg-white rounded-sm p-0.5"><img src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg" alt="Google" className="w-4 h-4" /></span>
            <span className="font-medium">Sign up with Google</span>
          </Button>
        </div>
        <div className="mt-4 text-sm flex justify-between">
          <Link href="/login"><a className="text-primary hover:underline">Already have an account?</a></Link>
        </div>
      </div>
    </div>
  );
}

