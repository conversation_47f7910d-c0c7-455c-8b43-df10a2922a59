import { useState, useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { Link, useLocation } from "wouter";

export default function VerificationPendingPage() {
  const { user, sendVerification, refreshUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [_, setLocation] = useLocation();
  const email = user?.email ?? "your email";

  // Auto-redirect when user becomes verified
  useEffect(() => {
    if (user?.emailVerified) {
      // User became verified, redirect to app
      setLocation("/app");
    }
  }, [user?.emailVerified, setLocation]);

  const resend = async () => {
    setLoading(true);
    setMessage(null);
    try {
      await sendVerification();
      setMessage("Verification email sent.");
    } catch (e) {
      setMessage("Failed to send verification email. Try again later.");
    } finally {
      setLoading(false);
    }
  };

  const refresh = async () => {
    setLoading(true);
    try {
      await refreshUser();
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center py-16 px-4">
      <div className="glass-card w-full max-w-lg rounded-xl p-8 text-center">
        <h2 className="text-2xl font-semibold mb-2">Verify your email</h2>
        <p className="text-sm text-muted-foreground mb-4">
          We sent a verification link to <span className="font-medium">{email}</span>.
          Please verify to access the app.
        </p>
        {message && <div className="mb-4 text-sm text-green-400">{message}</div>}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button className="gradient-button" onClick={resend} disabled={loading}>
            {loading ? "Sending…" : "Resend verification email"}
          </Button>
          <Button variant="outline" onClick={refresh} disabled={loading}>
            I verified, refresh
          </Button>
          <Link href="/login"><a className="text-primary text-sm self-center">Back to login</a></Link>
        </div>
      </div>
    </div>
  );
}

