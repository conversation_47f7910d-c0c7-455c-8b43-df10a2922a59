import { useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/context/AuthContext";

export default function ProtectedRoute({ children }: { children: JSX.Element }) {
  const { user, loading } = useAuth();
  const [_, setLocation] = useLocation();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        setLocation("/login");
      } else if (!user.emailVerified) {
        setLocation("/verify-pending");
      }
      // Note: Redirect to /app is now handled automatically by AuthContext
      // when user becomes verified
    }
  }, [loading, user, setLocation]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="glass-card px-6 py-4 rounded-lg">Checking authentication…</div>
      </div>
    );
  }

  if (!user || !user.emailVerified) return null;
  return children;
}

