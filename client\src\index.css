@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Glass Theme Color Variables */
    --background: 220 20% 6%;
    --foreground: 220 15% 85%;
    --card: 220 15% 15%;
    --card-foreground: 220 15% 85%;
    --popover: 220 15% 12%;
    --popover-foreground: 220 15% 85%;
    --primary: 220 15% 85%;
    --primary-foreground: 220 20% 10%;
    --secondary: 220 15% 25%;
    --secondary-foreground: 220 15% 85%;
    --muted: 220 15% 35%;
    --muted-foreground: 220 15% 65%;
    --accent: 220 40% 65%;
    --accent-foreground: 220 15% 85%;
    --destructive: 0 62% 50%;
    --destructive-foreground: 220 15% 85%;
    --border: 220 15% 30%;
    --input: 220 15% 25%;
    --ring: 220 40% 65%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 220 15% 12%;
    --sidebar-foreground: 220 15% 85%;
    --sidebar-primary: 220 15% 85%;
    --sidebar-primary-foreground: 220 20% 10%;
    --sidebar-accent: 220 40% 65%;
    --sidebar-accent-foreground: 220 15% 85%;
    --sidebar-border: 220 15% 30%;
    --sidebar-ring: 220 40% 65%;
    --radius: 0.75rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    background: linear-gradient(135deg, hsl(220, 20%, 6%) 0%, hsl(220, 25%, 8%) 100%);
    min-height: 100vh;
  }

  /* Glass effect base styles */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.3);
  }

  .glass-nav {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);
  }

  /* Gradient Button Styles */
  .gradient-button {
    background: linear-gradient(to right, rgba(59, 130, 246, 0.9), rgba(79, 70, 229, 0.9));
    color: white;
    border: none;
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.4);
    transition: all 0.3s ease;
  }

  .gradient-button:hover {
    background: linear-gradient(to right, rgba(37, 99, 235, 1), rgba(67, 56, 202, 1));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.5);
  }

  .gradient-button-secondary {
    background: linear-gradient(to right, rgba(168, 85, 247, 0.9), rgba(139, 92, 246, 0.9));
    color: white;
    border: none;
    box-shadow: 0 4px 14px 0 rgba(168, 85, 247, 0.4);
    transition: all 0.3s ease;
  }

  .gradient-button-secondary:hover {
    background: linear-gradient(to right, rgba(147, 51, 234, 1), rgba(126, 34, 206, 1));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px 0 rgba(168, 85, 247, 0.5);
  }

  .gradient-button-destructive {
    background: linear-gradient(to right, rgba(239, 68, 68, 0.9), rgba(225, 29, 72, 0.9));
    color: white;
    border: none;
    box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.4);
    transition: all 0.3s ease;
  }

  .gradient-button-destructive:hover {
    background: linear-gradient(to right, rgba(220, 38, 38, 1), rgba(190, 18, 60, 1));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px 0 rgba(239, 68, 68, 0.5);
  }

  /* Legacy glass button styles - keeping for backward compatibility */
  .glass-button {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
  }

  .glass-button:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px 0 rgba(31, 38, 135, 0.4);
  }

  .glass-input {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
  }

  .glass-input:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  }
}