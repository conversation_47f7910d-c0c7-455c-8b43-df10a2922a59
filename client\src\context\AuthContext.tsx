import { createContext, useContext, useEffect, useMemo, useState, useCallback } from "react";
import { useLocation } from "wouter";
import { auth } from "@/lib/firebase";
import {
  User,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  sendEmailVerification,
  signOut,
  setPersistence,
  browserLocalPersistence,
  GoogleAuthProvider,
  signInWithPopup,
  reload,
} from "firebase/auth";

interface AuthContextValue {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  signup: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  sendVerification: () => Promise<void>;
  refreshUser: () => Promise<void>;
  redirectToApp: () => void;
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [_, setLocation] = useLocation();

  // Redirect helper function with cleanup
  const redirectToApp = useCallback(() => {
    // Clear any error states before redirecting
    setError(null);
    // Use setTimeout to ensure state updates are processed
    setTimeout(() => {
      setLocation("/app");
    }, 0);
  }, [setLocation]);

  useEffect(() => {
    // Ensure auth persistence across sessions
    setPersistence(auth, browserLocalPersistence).catch(() => {});

    const unsub = onAuthStateChanged(auth, (u) => {
      const wasUnverified = user && !user.emailVerified;
      const nowVerified = u && u.emailVerified;
      const wasLoading = loading;

      setUser(u);
      setLoading(false);

      // Auto-redirect if user just became verified or if user is already verified and we just finished loading
      if ((wasUnverified && nowVerified) || (wasLoading && u && u.emailVerified)) {
        redirectToApp();
      }
    });
    return () => unsub();
  }, [user, loading, redirectToApp]);

  const login = async (email: string, password: string) => {
    setError(null);
    try {
      await signInWithEmailAndPassword(auth, email, password);
      // Success - redirect will be handled by onAuthStateChanged
    } catch (e) {
      setError(mapAuthError(e));
      throw e;
    }
  };

  const loginWithGoogle = async () => {
    setError(null);
    try {
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);
      // Success - redirect will be handled by onAuthStateChanged
    } catch (e) {
      setError(mapAuthError(e));
      throw e;
    }
  };

  const signup = async (email: string, password: string) => {
    setError(null);
    try {
      const cred = await createUserWithEmailAndPassword(auth, email, password);
      if (cred.user) {
        await sendEmailVerification(cred.user);
      }
    } catch (e) {
      setError(mapAuthError(e));
      throw e;
    }
  };

  const logout = async () => {
    setError(null);
    await signOut(auth).catch((e) => {
      setError(mapAuthError(e));
      throw e;
    });
  };

  const resetPassword = async (email: string) => {
    setError(null);
    await sendPasswordResetEmail(auth, email).catch((e) => {
      setError(mapAuthError(e));
      throw e;
    });
  };

  const sendVerification = async () => {
    if (!auth.currentUser) return;
    await sendEmailVerification(auth.currentUser);
  };

  const refreshUser = async () => {
    if (auth.currentUser) {
      await reload(auth.currentUser);
      // onAuthStateChanged will eventually fire, but to be snappy we set state here too
      setUser({ ...auth.currentUser });
    }
  };

  const value = useMemo(
    () => ({ user, loading, error, login, loginWithGoogle, signup, logout, resetPassword, sendVerification, refreshUser, redirectToApp }),
    [user, loading, error, redirectToApp]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error("useAuth must be used within AuthProvider");
  return ctx;
}

function mapAuthError(err: any): string {
  const code = err?.code || "unknown";
  switch (code) {
    case "auth/invalid-email":
      return "Invalid email address";
    case "auth/user-disabled":
      return "This account has been disabled";
    case "auth/user-not-found":
      return "No user found with these credentials";
    case "auth/wrong-password":
      return "Incorrect password";
    case "auth/email-already-in-use":
      return "Email already in use";
    case "auth/weak-password":
      return "Password should be at least 6 characters";
    case "auth/popup-closed-by-user":
      return "Sign-in popup was closed";
    default:
      return "Authentication error. Please try again.";
  }
}

